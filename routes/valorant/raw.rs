use crate::methods::utils::{ get_match_b2, get_match_mongo, RateLimiting };
use crate::structs::enums::RiotPlatforms;
use crate::structs::errors::{get_base_error, SendError, json_response};
use crate::structs::responses::{RawV1Response, RawV1ResponseData, RawV1ErrorData};
use crate::structs::http_clients::{ redis_fetch_ipv6, RedisFetchIPv6Options };
use crate::{ build_riot_headers, check_affinity, check_platform, error_handler, structs::post_payloads::{ RawV1Payload, RawV1PayloadValues }, AppState, ErrorCodes };
use axum::body::Body;
use axum::extract::State;
use axum::response::Response;
use axum::{ debug_handler, Extension, Json };
use futures::future::join_all;
use serde_json::json;
use std::sync::Arc;
use uuid::Uuid;
use valorant_api::response_types::competitive_updates_v1::CompetitiveUpdatesV1;
use valorant_api::response_types::matchhistory_v1::MatchHistoryV1;
use valorant_api::response_types::mmrdetails_v1::MMRDetailsV1;

#[utoipa::path(
	post,
	path = "/valorant/v1/raw",
	tag = "valorant",
	request_body = RawV1Payload,
	responses(
		(status = 200, description = "Raw Riot API data retrieved successfully", body = RawV1Response),
		(status = 400, description = "Bad Request", body = SendError),
		(status = 404, description = "Resource not found", body = SendError),
		(status = 500, description = "Internal Server Error", body = SendError)
	)
)]
#[debug_handler]
#[allow(non_snake_case)]
pub async fn Raw(State(app_state): State<Arc<AppState>>, Extension(extension): Extension<Arc<RateLimiting>>, Json(payload): Json<RawV1Payload>) -> Response {
	let validate_region = check_affinity(&payload.region);
	if !validate_region {
		return error_handler(vec![ErrorCodes::InvalidRegion]);
	}
	let region = if ["br", "latam"].iter().any(|x| x == &payload.region.to_lowercase()) { String::from("na") } else { payload.region.to_lowercase() };
	let platform = if payload.platform.is_some() { payload.platform.clone().unwrap().to_lowercase() } else { "pc".to_string() };
	if !check_platform(&platform) {
		return error_handler(vec![ErrorCodes::InvalidPlatform]);
	}
	let url_value = match &payload.value {
		RawV1PayloadValues::String(url) => url,
		RawV1PayloadValues::Vector(vector) => vector[0].as_str(),
	};
	let queries = match &payload.queries {
		Some(queries) => queries,
		None => "",
	};
	let url = match payload.type_.as_str() {
		"matchdetails" => format!("https://pd.{}.a.pvp.net/match-details/v1/matches/{}{}", &region, &url_value, &queries),
		"matchhistory" => format!("https://pd.{}.a.pvp.net/match-history/v1/history/{}{}", &region, &url_value, &queries),
		"mmr" => format!("https://pd.{}.a.pvp.net/mmr/v1/players/{}{}", &region, &url_value, &queries),
		"competitiveupdates" => format!("https://pd.{}.a.pvp.net/mmr/v1/players/{}/competitiveupdates{}", &region, &url_value, &queries),
		_ => {
			return error_handler(vec![ErrorCodes::UnknownRawType]);
		}
	};

	let client = app_state.client.clone();
	//let redis = app_state.pool.clone();
	//let conn = redis.get().await.expect("Failed to get Redis connection from pool");
	let conn = app_state.redis.clone();
	let rl = extension.clone();

	if payload.type_ == "matchdetails" {
		let values = match &payload.value {
			RawV1PayloadValues::String(url) => vec![url.clone()],
			RawV1PayloadValues::Vector(vector) => vector.clone(),
		};
		if values.iter().any(|i| Uuid::parse_str(&i).is_err()) {
			return error_handler(vec![ErrorCodes::InvalidUUID]);
		}
		let mut fetch: Vec<_> = vec![];
		for i in values.clone() {
			fetch.push(get_match_b2(&client, conn.clone(), i, region.clone()));
		}
		let results = join_all(fetch).await;
		let mut http_requests: usize = 0;
		let mixed_result: Vec<Value> = results
			.iter()
			.map(|i| {
				if !i.is_from_db {
					http_requests += 1;
				}
				if i.error {
					json!({"error": i.error, "code": i.status, "id": i.id})
				} else {
					json!(i.data)
				}
			})
			.collect();
		rl.background_requests.fetch_add(http_requests, std::sync::atomic::Ordering::SeqCst);
		let response_data = if mixed_result.len() <= 1 {
			RawV1ResponseData::Single(mixed_result[0].clone())
		} else {
			RawV1ResponseData::Multiple(mixed_result)
		};
		let response = RawV1Response {
			status: 200,
			data: response_data,
		};
		return json_response(&response, 200);
	}
	let validate_uuid = Uuid::parse_str(&url_value);
	if validate_uuid.is_err() {
		return error_handler(vec![ErrorCodes::InvalidUUID]);
	}
	let riot_headers = build_riot_headers(&RiotPlatforms::from_str(&platform).unwrap()).await;
	match payload.type_.as_str() {
		"matchhistory" => {
			let fetch = redis_fetch_ipv6::<MatchHistoryV1>(RedisFetchIPv6Options {
				url,
				store: if queries.len() > 0 {
					format!("match-history;v1;{};{};{};{}", region, &platform, url_value, queries)
				} else {
					format!("match-history;v1;{};{};{}", region, &platform, url_value)
				},
				headers: riot_headers,
				redis_client: Some(conn),
				..RedisFetchIPv6Options::default()
			}).await;
			match fetch {
				Ok(v) => {
					rl.redis_cache_ttl.store(v.ttl as isize, std::sync::atomic::Ordering::SeqCst);
					if !v.is_from_redis {
						rl.background_requests.fetch_add(1, std::sync::atomic::Ordering::SeqCst);
					}
					let response = RawV1Response {
						status: 200,
						data: RawV1ResponseData::Single(serde_json::to_value(&v.data).unwrap()),
					};
					json_response(&response, 200)
				}
				Err(v) => error_handler(vec![get_base_error(v.status)]),
			}
		}
		"mmr" => {
			let fetch = redis_fetch_ipv6::<MMRDetailsV1>(RedisFetchIPv6Options {
				url,
				store: if queries.len() > 0 {
					format!("mmr-player;v1;{};{};{};{}", region, &platform, url_value, queries)
				} else {
					format!("mmr-player;v1;{};{};{}", region, &platform, url_value)
				},
				redis_client: Some(conn),
				headers: riot_headers,
				..RedisFetchIPv6Options::default()
			}).await;
			match fetch {
				Ok(v) => {
					rl.redis_cache_ttl.store(v.ttl as isize, std::sync::atomic::Ordering::SeqCst);
					if !v.is_from_redis {
						rl.background_requests.fetch_add(1, std::sync::atomic::Ordering::SeqCst);
					}
					let response = RawV1Response {
						status: 200,
						data: RawV1ResponseData::Single(serde_json::to_value(&v.data).unwrap()),
					};
					json_response(&response, 200)
				}
				Err(v) => error_handler(vec![get_base_error(v.status)]),
			}
		}
		"competitiveupdates" => {
			let fetch = redis_fetch_ipv6::<CompetitiveUpdatesV1>(RedisFetchIPv6Options {
				url,
				redis_client: Some(conn),
				store: if queries.len() > 0 {
					format!("mmr-history;v1;{};{};{};{}", region, &platform, url_value, queries)
				} else {
					format!("mmr-history;v1;{};{};{}", region, &platform, url_value)
				},
				headers: riot_headers,
				..RedisFetchIPv6Options::default()
			}).await;
			match fetch {
				Ok(v) => {
					rl.redis_cache_ttl.store(v.ttl as isize, std::sync::atomic::Ordering::SeqCst);
					if !v.is_from_redis {
						rl.background_requests.fetch_add(1, std::sync::atomic::Ordering::SeqCst);
					}
					let response = RawV1Response {
						status: 200,
						data: RawV1ResponseData::Single(serde_json::to_value(&v.data).unwrap()),
					};
					json_response(&response, 200)
				}
				Err(v) => error_handler(vec![get_base_error(v.status)]),
			}
		}
		_ => error_handler(vec![ErrorCodes::UnknownRawType]),
	}
}
