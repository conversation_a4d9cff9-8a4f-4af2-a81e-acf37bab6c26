use redis::RedisResult;
use crate::methods::utils::RateLimiting;
use crate::structs::database::{ APITokens, Auth, PremierConferencesDB, PremierSeasonDB, Versions };
use crate::structs::enums::RiotPlatforms;
use crate::structs::errors::{ error_handler, ErrorCodes };
use crate::structs::external::{ B2Auth, Proxys };
use crate::structs::helper::{ get_c_season_premier, roman_to_number };
use crate::structs::http_clients::{ fetch, FetchOptions };
use crate::structs::jobs::{
	load_website,
	mongo_to_s3_migration_wrapper,
	update_player_leaderboard_job,
	update_premier_metadata_database_job,
	update_premier_teams_database_job,
};
use crate::structs::pvp_api::{ FModelPremierColors, FModelPremierColorsRow };
use crate::structs::valorant_api_officer::{
	<PERSON>orantAPIBuddys,
	ValorantAPIContentTiers,
	ValorantAPIFlex,
	ValorantAPIGamePodsData,
	ValorantAPIPlayerCards,
	ValorantAPIPlayerTitles,
	ValorantAPISprays,
	ValorantAPIWeaponSkins,
};
use crate::structs::valorant_api_officer::{ ValorantAPIGamePods, ValorantAPIGamemodeQueues, ValorantAPIGamemodes, ValorantAPISeasons };
use axum::extract::rejection::JsonRejection;
use axum::extract::{ FromRef, Path, Query, Request, State };
use axum::http::HeaderName;
use axum::middleware::{ from_fn_with_state, Next };
use axum::response::Response;
use axum::routing::{ delete, get, post };
use axum::Router;
use chrono::Utc;
use dotenv::dotenv;
use env_logger;
use futures::{ Future, TryStreamExt };
use mimalloc::MiMalloc;
use mongodb::bson::{ DateTime, Document };
use mongodb::options::ClientOptions;
use mongodb::{ bson::doc, Client };
use redis::aio::MultiplexedConnection;
use reqwest::header::HeaderMap;
use reqwest::Proxy;
use serde::{ de::DeserializeOwned, Deserialize, Serialize };
use sqlx::postgres::PgPoolOptions;
use sqlx::{ PgPool, Postgres };
use std::env;
use std::error::Error;
use std::sync::atomic::{ AtomicIsize, AtomicUsize, Ordering };
use std::sync::Arc;
use std::time::Duration;
use std::{ collections::HashMap, fs };
use arc_swap::ArcSwap;
use axum::body::Body;
use lazy_static::lazy::Lazy;
use structs::valorant_api_officer::{ ValorantAPIAgents, ValorantAPIMaps };
use tokio::sync::RwLock;
use tokio::{ join, time::Instant };
use utoipa::OpenApi;
use utoipa_swagger_ui::SwaggerUi;
use uuid::Uuid;
use redis::AsyncCommands;
use valorant_assets_api::gears::get_gears;
use valorant_assets_api::models::gear::Gear;
use valorant_assets_api::models::weapon::Weapon;
use valorant_assets_api::weapons::get_weapons;
use once_cell::sync::{ Lazy as OLazy, OnceCell };
use serde_json::to_vec;

mod compression;
mod methods;
mod routes;
mod structs;
mod openapi;

#[global_allocator]
static GLOBAL: MiMalloc = MiMalloc;

#[derive(Clone)]
pub struct HDDClient(Client);

#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct SeasonShortIdCombo {
	pub season: String,
	pub short_id: String,
}

pub static IS_PROD: OLazy<bool> = OLazy::new(|| std::env::var("IS_PROD").unwrap_or_else(|_| "false".into()) == "true");
pub static RIOT_TOKEN: OLazy<String> = OLazy::new(|| std::env::var("RIOT_TOKEN").unwrap());
pub static IPV6_PROXY: OLazy<String> = OLazy::new(|| std::env::var("IPV6_PROXY").unwrap());

pub static DEFAULT_CLIENT: OnceCell<Arc<reqwest::Client>> = OnceCell::new();
pub static ALTERNATIVE_PROXY_CLIENT: OnceCell<Arc<reqwest::Client>> = OnceCell::new();
pub static IPV6_CLIENT: OnceCell<Arc<reqwest::Client>> = OnceCell::new();

pub static OFFICER_API_MAPS: OLazy<ArcSwap<Vec<structs::valorant_api_officer::ValorantAPIMap>>> = OLazy::new(|| { ArcSwap::from(Arc::new(vec![])) });
pub static OFFICER_API_AGENTS: OLazy<ArcSwap<Vec<structs::valorant_api_officer::ValorantAPIAgent>>> = OLazy::new(|| { ArcSwap::from(Arc::new(vec![])) });
pub static OFFICER_API_WEAPONS: OLazy<ArcSwap<Vec<Weapon>>> = OLazy::new(|| { ArcSwap::from(Arc::new(vec![])) });
pub static OFFICER_API_GAME_PODS: OLazy<ArcSwap<HashMap<String, String>>> = OLazy::new(|| { ArcSwap::from(Arc::new(HashMap::new())) });
pub static OFFICER_API_GEARS: OLazy<ArcSwap<Vec<Gear>>> = OLazy::new(|| { ArcSwap::from(Arc::new(vec![])) });
pub static OFFICER_API_SEASONS: OLazy<ArcSwap<Vec<structs::valorant_api_officer::ValorantAPISeasonsData>>> = OLazy::new(|| { ArcSwap::from(Arc::new(vec![])) });
pub static OFFICER_API_SKINS: OLazy<ArcSwap<Vec<structs::valorant_api_officer::ValorantAPIWeaponSkin>>> = OLazy::new(|| { ArcSwap::from(Arc::new(vec![])) });
pub static OFFICER_API_SPRAYS: OLazy<ArcSwap<Vec<structs::valorant_api_officer::ValorantAPISpray>>> = OLazy::new(|| { ArcSwap::from(Arc::new(vec![])) });
pub static OFFICER_API_BUDDIES: OLazy<ArcSwap<Vec<structs::valorant_api_officer::ValorantAPIBuddy>>> = OLazy::new(|| { ArcSwap::from(Arc::new(vec![])) });
pub static OFFICER_API_PLAYER_CARDS: OLazy<ArcSwap<Vec<structs::valorant_api_officer::ValorantAPIPlayerCard>>> = OLazy::new(|| { ArcSwap::from(Arc::new(vec![])) });
pub static OFFICER_API_CONTENT_TIERS: OLazy<ArcSwap<Vec<structs::valorant_api_officer::ValorantAPIContentTier>>> = OLazy::new(|| { ArcSwap::from(Arc::new(vec![])) });
pub static OFFICER_API_CONTENT_GAMEMODES: OLazy<ArcSwap<Vec<structs::valorant_api_officer::ValorantAPIGamemodesData>>> = OLazy::new(|| {
	ArcSwap::from(Arc::new(vec![]))
});
pub static OFFICER_API_CONTENT_GAMEMODES_QUEUES: OLazy<ArcSwap<Vec<structs::valorant_api_officer::ValorantAPIGamemodeQueuesData>>> = OLazy::new(|| {
	ArcSwap::from(Arc::new(vec![]))
});
pub static OFFICER_API_PLAYER_TITLES: OLazy<ArcSwap<Vec<structs::valorant_api_officer::ValorantAPIPlayerTitle>>> = OLazy::new(|| { ArcSwap::from(Arc::new(vec![])) });
pub static OFFICER_API_FLEX: OLazy<ArcSwap<Vec<structs::valorant_api_officer::ValorantAPIFlexItem>>> = OLazy::new(|| { ArcSwap::from(Arc::new(vec![])) });

pub static VALORANT_SEASON_SHORT_IDS: OLazy<ArcSwap<Vec<SeasonShortIdCombo>>> = OLazy::new(|| ArcSwap::from(Arc::new(vec![])));
pub static VALORANT_REGULAR_CURRENT_SEASON: OLazy<ArcSwap<String>> = OLazy::new(|| ArcSwap::from(Arc::new(String::new())));
pub static VALORANT_AUTH: OLazy<ArcSwap<HashMap<String, String>>> = OLazy::new(|| ArcSwap::from(Arc::new(HashMap::new())));
pub static VALORANT_VERSIONS: OLazy<ArcSwap<Vec<Versions>>> = OLazy::new(||
	ArcSwap::from(
		Arc::new(
			vec![Versions {
				region: String::from("na"),
				branch: String::from("release-08.04"),
				build_date: String::from("Mar 1 2024"),
				build_ver: String::from("08.04.00.2324912"),
				last_checked: String::from("2024-03-08T12:03:30.736Z"),
				patch_url: String::from("https://valorant.secure.dyn.riotcdn.net/channels/public/releases/6F1B1A9C49ACC867.manifest"),
				version: 1,
				version_for_api: String::from("release-08.04-shipping-4-2324912"),
			}]
		)
	)
);
pub static VALORANT_PREMIER_CURRENT_SEASON: OLazy<ArcSwap<String>> = OLazy::new(|| ArcSwap::from(Arc::new(String::new())));
pub static VALORANT_PREMIER_CONFERENCES: OLazy<ArcSwap<Vec<PremierConferencesDB>>> = OLazy::new(|| ArcSwap::from(Arc::new(vec![])));
pub static VALORANT_PREMIER_COLORS: OLazy<ArcSwap<Vec<FModelPremierColorsRow>>> = OLazy::new(|| ArcSwap::from(Arc::new(vec![])));
pub static PROXYS: OLazy<ArcSwap<Vec<structs::external::Proxy>>> = OLazy::new(|| ArcSwap::from(Arc::new(vec![])));
pub static B2_AUTH: OLazy<ArcSwap<B2Data>> = OLazy::new(||
	ArcSwap::from(
		Arc::new(B2Data {
			authorization_token: String::new(),
			api_url: "".to_string(),
			s3_api_url: "".to_string(),
			bucket_id: "".to_string(),
			bucket_name: "".to_string(),
		})
	)
);
pub static S3_CLIENT: OLazy<ArcSwap<minio_rsc::Minio>> = OLazy::new(||
	ArcSwap::from(
		Arc::new(
			minio_rsc::Minio::builder().endpoint("s3.eu-central-003.backblazeb2.com").provider(minio_rsc::provider::StaticProvider::new("", "", None)).build().unwrap()
		)
	)
);

// Read-only statics as OLazy (no mutability needed)
pub static AFFINITIES: OLazy<Vec<String>> = OLazy::new(|| {
	vec!["eu", "na", "ap", "kr", "br", "latam"]
		.iter()
		.map(|x| x.to_string())
		.collect()
});
pub static REGIONS: OLazy<Vec<String>> = OLazy::new(|| {
	vec!["na", "eu", "ap", "kr"]
		.iter()
		.map(|x| x.to_string())
		.collect()
});
pub static VALORANT_TYPE_IDS: OLazy<HashMap<&'static str, &'static str>> = OLazy::new(|| {
	vec![
		("e7c63390-eda7-46e0-bb7a-a6abdacd2433", "skin_level"),
		("3ad1b2b2-acdb-4524-852f-954a76ddae0a", "skin_chroma"),
		("01bb38e1-da47-4e6a-9b3d-945fe4655707", "agent"),
		("f85cb6f7-33e5-4dc8-b609-ec7212301948", "contract_definition"),
		("dd3bf334-87f3-40bd-b043-682a57a8dc3a", "buddy"),
		("d5f120f8-ff8c-4aac-92ea-f2b5acbe9475", "spray"),
		("3f296c07-64c3-494c-923b-fe692a4fa1bd", "player_card"),
		("de7caa6b-adf7-4588-bbd1-143831e786c6", "player_title"),
		("03a572de-4234-31ed-d344-ababa488f981", "flex")
	]
		.into_iter()
		.collect()
});

pub static VALORANT_TIERS: OLazy<HashMap<usize, (&'static str, bool)>> = OLazy::new(|| {
	vec![
		(0, ("Unrated", false)),
		(1, ("Unknown 1", false)),
		(2, ("Unknown 2", false)),
		(3, ("Iron 1", false)),
		(4, ("Iron 2", false)),
		(5, ("Iron 3", false)),
		(6, ("Bronze 1", true)),
		(7, ("Bronze 2", false)),
		(8, ("Bronze 3", false)),
		(9, ("Silver 1", true)),
		(10, ("Silver 2", false)),
		(11, ("Silver 3", false)),
		(12, ("Gold 1", true)),
		(13, ("Gold 2", false)),
		(14, ("Gold 3", false)),
		(15, ("Platinum 1", true)),
		(16, ("Platinum 2", false)),
		(17, ("Platinum 3", false)),
		(18, ("Diamond 1", true)),
		(19, ("Diamond 2", false)),
		(20, ("Diamond 3", false)),
		(21, ("Ascendant 1", true)),
		(22, ("Ascendant 2", false)),
		(23, ("Ascendant 3", false)),
		(24, ("Immortal 1", true)),
		(25, ("Immortal 2", false)),
		(26, ("Immortal 3", false)),
		(27, ("Radiant", false))
	]
		.into_iter()
		.collect()
});

pub static VALORANT_TIERS_OLD: OLazy<HashMap<usize, &'static str>> = OLazy::new(|| {
	vec![
		(0, "Unrated"),
		(1, "Unknown 1"),
		(2, "Unknown 2"),
		(3, "Iron 1"),
		(4, "Iron 2"),
		(5, "Iron 3"),
		(6, "Bronze 1"),
		(7, "Bronze 2"),
		(8, "Bronze 3"),
		(9, "Silver 1"),
		(10, "Silver 2"),
		(11, "Silver 3"),
		(12, "Gold 1"),
		(13, "Gold 2"),
		(14, "Gold 3"),
		(15, "Platinum 1"),
		(16, "Platinum 2"),
		(17, "Platinum 3"),
		(18, "Diamond 1"),
		(19, "Diamond 2"),
		(20, "Diamond 3"),
		(21, "Immortal 1"),
		(22, "Immortal 2"),
		(23, "Immortal 3"),
		(24, "Radiant")
	]
		.into_iter()
		.collect()
});

pub static GLZ_URLS: OLazy<HashMap<&'static str, &'static str>> = OLazy::new(|| {
	vec![
		("eu", "https://glz-eu-1.eu.a.pvp.net"),
		("na", "https://glz-na-1.na.a.pvp.net"),
		("br", "https://glz-br-1.na.a.pvp.net"),
		("kr", "https://glz-kr-1.kr.a.pvp.net"),
		("latam", "https://glz-latam-1.na.a.pvp.net"),
		("ap", "https://glz-ap-1.ap.a.pvp.net")
	]
		.into_iter()
		.collect()
});

pub static PREMIER_URLS: OLazy<HashMap<&'static str, &'static str>> = OLazy::new(|| {
	vec![
		("eu", "https://euc1-red.pp.sgp.pvp.net"),
		("na", "https://usw2-red.pp.sgp.pvp.net"),
		("br", "https://usw2-red.pp.sgp.pvp.net"),
		("kr", "https://apne1-red.pp.sgp.pvp.net"),
		("latam", "https://usw2-red.pp.sgp.pvp.net"),
		("ap", "https://apse1-red.pp.sgp.pvp.net")
	]
		.into_iter()
		.collect()
});

//Routes
async fn default() -> Response {
	error_handler(vec![ErrorCodes::Base404])
}

//Utils
#[derive(Deserialize, Serialize, Debug, Clone)]
pub struct B2Data {
	pub authorization_token: String,
	pub api_url: String,
	pub s3_api_url: String,
	pub bucket_id: String,
	pub bucket_name: String,
}

pub enum OfficerAPI {
	Agents(ValorantAPIAgents),
	Maps(ValorantAPIMaps),
	WeaponSkins(ValorantAPIWeaponSkins),
	Buddies(ValorantAPIBuddys),
	Sprays(ValorantAPISprays),
	PlayerCards(ValorantAPIPlayerCards),
	PlayerTitles(ValorantAPIPlayerTitles),
}

const VALORANT_PLATFORMS: [&str; 2] = ["pc", "console"];
const VALORANT_REGIONS: [&str; 6] = ["na", "eu", "ap", "kr", "br", "latam"];
const VALORANT_WEBSITE_COUNTRIES: [&str; 14] = [
	"en-us",
	"en-gb",
	"de-de",
	"es-es",
	"fr-fr",
	"it-it",
	"ru-ru",
	"tr-tr",
	"es-mx",
	"ja-jp",
	"ko-kr",
	"pt-br",
	"pl-pl",
	"vi-vn",
];
const VALORANT_CONTENT_LOCALS: [&str; 19] = [
	"ar-AE",
	"de-DE",
	"en-GB",
	"en-US",
	"es-ES",
	"es-MX",
	"fr-FR",
	"id-ID",
	"it-IT",
	"ja-JP",
	"ko-KR",
	"pl-PL",
	"pt-BR",
	"ru-RU",
	"th-TH",
	"tr-TR",
	"vi-VN",
	"zh-CN",
	"zh-TW",
];
const VALORANT_WEBSITE_CATEGORIES: [&str; 6] = ["game_updates", "dev", "esports", "announcements", "patch_notes", "community"];

pub fn init_http_clients() {
	let default = reqwest::Client::builder().build().expect("Failed to build default client");

	DEFAULT_CLIENT.set(Arc::new(default)).expect("DEFAULT_CLIENT already set");

	let alternative_proxy = std::env::var("ALTERNATIVE_PROXY").unwrap_or_default();
	let alt_client = if !alternative_proxy.is_empty() {
		reqwest::Client
			::builder()
			.proxy(reqwest::Proxy::all(&alternative_proxy).expect("Invalid ALTERNATIVE_PROXY"))
			.build()
			.expect("Failed to build alternative proxy client")
	} else {
		reqwest::Client::new()
	};
	ALTERNATIVE_PROXY_CLIENT.set(Arc::new(alt_client)).expect("ALTERNATIVE_PROXY_CLIENT already set");

	let ipv6_proxy = std::env::var("IPV6_PROXY").unwrap_or_default();
	let ipv6_auth = std::env::var("IPV6_PROXY_AUTH").unwrap_or_default();
	let ipv6_client = if !ipv6_proxy.is_empty() && !ipv6_auth.is_empty() {
		let mut split = ipv6_auth.split(':');
		let user = split.next().unwrap_or("");
		let pass = split.next().unwrap_or("");
		let mut proxy = Proxy::all(&ipv6_proxy).expect("Invalid IPV6_PROXY");
		proxy = proxy.basic_auth(user, pass);
		let mut header = HeaderMap::new();
		header.insert("Connection", "close".parse().unwrap());
		reqwest::Client::builder().proxy(proxy).default_headers(header).build().expect("Failed to build ipv6 client")
	} else {
		reqwest::Client::new()
	};
	IPV6_CLIENT.set(Arc::new(ipv6_client)).expect("IPV6_CLIENT already set");
}

pub fn json_reponse<T: Serialize>(data: &T) -> Response {
	Response::builder()
		.status(200)
		.header("Content-Type", "application/json")
		.body(Body::from(to_vec(&data).unwrap()))
		.unwrap()
}

pub fn get_db<T: DeserializeOwned + std::marker::Send + std::marker::Sync>(client: &Client, collection: &str, db: Option<&str>) -> mongodb::Collection<T> {
	let db = db.unwrap_or_else(|| {
		if cfg!(debug_asertions) {
			"INGAME-API-DEV"
		} else {
			if *IS_PROD { "INGAME-API" } else { "INGAME-API-DEV" }
		}
	});
	client.database(db).collection::<T>(collection)
}

pub fn get_db_hdd<T: DeserializeOwned + std::marker::Send + std::marker::Sync>(client: &Client, collection: &str, db: Option<&str>) -> mongodb::Collection<T> {
	let db = db.unwrap_or_else(|| if cfg!(debug_assertions) { "API" } else { "API" });
	client.database(db).collection::<T>(collection)
}

async fn build_riot_headers(platform: &RiotPlatforms) -> HeaderMap {
	let platforms = match platform {
		RiotPlatforms::PC =>
			"ew0KCSJwbGF0Zm9ybVR5cGUiOiAiUEMiLA0KCSJwbGF0Zm9ybU9TIjogIldpbmRvd3MiLA0KCSJwbGF0Zm9ybU9TVmVyc2lvbiI6ICIxMC4wLjE5MDQyLjEuMjU2LjY0Yml0IiwNCgkicGxhdGZvcm1DaGlwc2V0IjogIlVua25vd24iDQp9",
		RiotPlatforms::CONSOLE =>
			"eyAgInBsYXRmb3JtVHlwZSI6ICJ4Ym94IiwgICJwbGF0Zm9ybU9TIjogIlhTWCIsICAicGxhdGZvcm1PU1ZlcnNpb24iOiAiMTAuMC4yNTM5OC40OTA4IiwgICJwbGF0Zm9ybUNoaXBzZXQiOiAiVW5rbm93biIsICAicGxhdGZvcm1EZXZpY2UiOiAiIn0=",
		_ =>
			"ew0KCSJwbGF0Zm9ybVR5cGUiOiAiUEMiLA0KCSJwbGF0Zm9ybU9TIjogIldpbmRvd3MiLA0KCSJwbGF0Zm9ybU9TVmVyc2lvbiI6ICIxMC4wLjE5MDQyLjEuMjU2LjY0Yml0IiwNCgkicGxhdGZvcm1DaGlwc2V0IjogIlVua25vd24iDQp9",
	};
	let mut headers = HeaderMap::new();
	let auth = VALORANT_AUTH.load_full();
	let version = VALORANT_VERSIONS.load_full();
	headers.insert(
		"authorization",
		format!("Bearer {}", auth.get("authorization").unwrap_or(&String::from("null")))
			.parse()
			.unwrap()
	);
	headers.insert("X-Riot-Entitlements-JWT", auth.get("entitlements").unwrap_or(&String::from("null")).parse().unwrap());
	headers.insert("X-Riot-ClientPlatform", platforms.parse().unwrap());
	headers.insert("X-Riot-ClientVersion", version.first().unwrap().version_for_api.parse().unwrap());
	headers.insert("user-agent", "ShooterGame/13 Windows/10.0.19043.1.256.64bit".parse().unwrap());
	headers
}

async fn build_riot_headers_hash_map(platform: &RiotPlatforms) -> HashMap<String, String> {
	let platforms = match platform {
		RiotPlatforms::PC =>
			"ew0KCSJwbGF0Zm9ybVR5cGUiOiAiUEMiLA0KCSJwbGF0Zm9ybU9TIjogIldpbmRvd3MiLA0KCSJwbGF0Zm9ybU9TVmVyc2lvbiI6ICIxMC4wLjE5MDQyLjEuMjU2LjY0Yml0IiwNCgkicGxhdGZvcm1DaGlwc2V0IjogIlVua25vd24iDQp9",
		RiotPlatforms::CONSOLE =>
			"eyAgInBsYXRmb3JtVHlwZSI6ICJ4Ym94IiwgICJwbGF0Zm9ybU9TIjogIlhTWCIsICAicGxhdGZvcm1PU1ZlcnNpb24iOiAiMTAuMC4yNTM5OC40OTA4IiwgICJwbGF0Zm9ybUNoaXBzZXQiOiAiVW5rbm93biIsICAicGxhdGZvcm1EZXZpY2UiOiAiIn0=",
		_ =>
			"ew0KCSJwbGF0Zm9ybVR5cGUiOiAiUEMiLA0KCSJwbGF0Zm9ybU9TIjogIldpbmRvd3MiLA0KCSJwbGF0Zm9ybU9TVmVyc2lvbiI6ICIxMC4wLjE5MDQyLjEuMjU2LjY0Yml0IiwNCgkicGxhdGZvcm1DaGlwc2V0IjogIlVua25vd24iDQp9",
	};
	let mut headers = HashMap::new();
	let auth = VALORANT_AUTH.load_full();
	let version = VALORANT_VERSIONS.load_full();
	headers.insert("authorization".to_string(), format!("Bearer {}", auth.get("authorization").unwrap_or(&String::from("null"))));
	headers.insert("X-Riot-Entitlements-JWT".to_string(), auth.get("entitlements").unwrap_or(&String::from("null")).parse().unwrap());
	headers.insert("X-Riot-ClientPlatform".to_string(), platforms.parse().unwrap());
	headers.insert("X-Riot-ClientVersion".to_string(), version.first().unwrap().version_for_api.parse().unwrap());
	headers.insert("user-agent".to_string(), "ShooterGame/13 Windows/10.0.19043.1.256.64bit".parse().unwrap());
	headers
}

async fn build_official_riot_headers() -> HeaderMap {
	let token = env::var("RIOT_TOKEN").unwrap();
	let mut headers = HeaderMap::new();
	headers.insert("X-Riot-Token", token.parse().unwrap());
	headers.insert("user-agent", "VALORANT_LABS_v9".parse().unwrap());
	headers
}

async fn build_official_riot_headers_hash_map() -> HashMap<String, String> {
	let token = env::var("RIOT_TOKEN").unwrap();
	let mut headers = HashMap::new();
	headers.insert("X-Riot-Token".to_string(), token.parse().unwrap());
	headers.insert("user-agent".to_string(), "VALORANT_LABS_v9".parse().unwrap());
	headers
}

fn check_affinity(affinity: &str) -> bool {
	let region = if ["br", "latam"].iter().any(|x| x == &affinity.to_lowercase()) { String::from("na") } else { affinity.to_lowercase() };
	VALORANT_REGIONS.contains(&region.as_str())
}

fn check_platform(platform: &str) -> bool {
	VALORANT_PLATFORMS.contains(&platform.to_lowercase().as_str())
}

/*pub struct CustomJson<T>(pub T);

#[async_trait]
impl<B, T> FromRequest<B> for CustomJson<T>
where
    B: Send + Sync,
    T: DeserializeOwned,
{
    type Rejection = Response;

    async fn from_request(request: Request, s: &'life0 B) -> Result<Self, Self::Rejection> {
        match Json::<T>::from_request(request, &()).await {
            Ok(Json(value)) => Ok(CustomJson(value)),
            Err(rejection) => Err(handle_json_error(rejection)),
        }
    }
}*/

fn handle_json_error(rejection: JsonRejection) -> Response {
	let error_message = match rejection {
		JsonRejection::JsonDataError(err) => format!("Invalid JSON data: {}", err),
		JsonRejection::JsonSyntaxError(err) => format!("Syntax error in JSON: {}", err),
		JsonRejection::MissingJsonContentType(err) => format!("Missing JSON content type: {}", err),
		_ => "Unknown JSON error".to_string(),
	};

	error_handler(vec![ErrorCodes::UserParsingError])
}

//Syncs
async fn website_job(client: Client) {
	tokio::time::sleep(Duration::from_secs(2 * 60)).await;
	loop {
		let before = Instant::now();
		println!("[JOBS][WEBSITE] Running Job...");
		{
			load_website(&client).await;
		}
		println!("[JOBS][WEBSITE] Synced in {}ms", before.elapsed().as_millis());
		tokio::time::sleep(Duration::from_secs(2 * 60)).await;
	}
}

async fn officer_api_job() {
	loop {
		let before = Instant::now();
		println!("[JOBS][OFFICER] Running Job...");
		{
			load_officer().await;
		}
		println!("[JOBS][OFFICER] Synced in {}ms", before.elapsed().as_millis());
		tokio::time::sleep(Duration::from_secs(60)).await;
	}
}

async fn sync_val_auth_job(client: Client) {
	loop {
		let before = Instant::now();
		println!("[JOBS][SYNC_VAL_AUTH] Running Job...");
		{
			sync_val_auth(&client).await;
		}
		println!("[JOBS][SYNC_VAL_AUTH] Synced in {}ms", before.elapsed().as_millis());
		tokio::time::sleep(tokio::time::Duration::from_secs(60)).await;
	}
}

async fn sync_val_versions_job(client: Client) {
	loop {
		let before = Instant::now();
		println!("[JOBS][SYNC_VAL_VERSIONS] Running Job...");
		{
			sync_version(&client).await;
		}
		println!("[JOBS][SYNC_VAL_VERSIONS] Synced in {}ms", before.elapsed().as_millis());
		tokio::time::sleep(tokio::time::Duration::from_secs(60)).await;
	}
}

async fn update_b2_job() {
	loop {
		let before = Instant::now();
		println!("[JOBS][UPDATE_B2] Running Job...");
		{
			update_b2().await;
		}
		println!("[JOBS][UPDATE_B2] Synced in {}ms", before.elapsed().as_millis());
		tokio::time::sleep(Duration::from_secs(60 * 60 * 12)).await;
	}
}

async fn update_b2() {
	// Umgebungsvariablen für Backblaze B2 Authentifizierung auslesen
	let b2_key_id = match env::var("B2_KEY_ID") {
		Ok(id) => id,
		Err(_) => {
			eprintln!("[B2 AUTH] Fehler beim Lesen der B2_KEY_ID Umgebungsvariable");
			return;
		}
	};

	let b2_key = match env::var("B2_KEY") {
		Ok(key) => key,
		Err(_) => {
			eprintln!("[B2 AUTH] Fehler beim Lesen der B2_KEY Umgebungsvariable");
			return;
		}
	};

	// Erstellen der Basis-Authentifizierung
	let auth_string = format!("{}:{}", b2_key_id, b2_key);
	let auth_base64 = base64::encode(&auth_string);

	// API-Aufruf zur B2-Authentifizierung
	let mut headers = HeaderMap::new();
	headers.insert("Authorization", format!("Basic {}", auth_base64).parse().unwrap());
	let fetch_ = fetch::<B2Auth>(FetchOptions {
		url: String::from("https://api.backblazeb2.com/b2api/v3/b2_authorize_account"),
		headers: Some(headers),
		..FetchOptions::default()
	}).await;

	if let Err(e) = fetch_ {
		eprintln!("[B2 AUTH] Fehler beim Abrufen der Authentifizierung: {:?}", e);
		return;
	}
	let auth_response = fetch_.unwrap();

	// Speichern der Authentifizierungsinformationen im globalen HashMap
	B2_AUTH.store(
		Arc::new(B2Data {
			authorization_token: auth_response.data.authorization_token.clone(),
			api_url: auth_response.data.api_info.storage_api.api_url.clone(),
			s3_api_url: auth_response.data.api_info.storage_api.s3_api_url.clone(),
			bucket_id: auth_response.data.api_info.storage_api.bucket_id.clone(),
			bucket_name: auth_response.data.api_info.storage_api.bucket_name.clone(),
		})
	);
	let mut b2_auth = B2_AUTH.load_full();
	println!("[B2 AUTH] Authentifizierung erfolgreich aktualisiert");
	println!("[B2 AUTH] API URL: {}", format!("{}/{}", b2_auth.s3_api_url.clone().replace("https://", ""), b2_auth.bucket_name.clone()));

	// S3 Minio Client erstellen
	let static_provider = minio_rsc::provider::StaticProvider::new(b2_key_id.clone(), b2_key.clone(), None);
	S3_CLIENT.store(
		Arc::new(
			minio_rsc::Minio
				::builder()
				.endpoint(b2_auth.s3_api_url.clone().replace("https://", ""))
				.secure(true)
				.virtual_hosted_style(true)
				.provider(static_provider)
				.build()
				.unwrap()
		)
	);
	match S3_CLIENT.load_full().list_buckets().await {
		Ok(buckets) => println!("{:#?}", buckets),
		Err(e) => {
			println!("S3 error: {:?}", e);
			if let Some(raw) = e.source() {
				println!("Underlying source: {:?}", raw);
			}
		}
	}
	println!("[B2 AUTH] S3 Minio Client erfolgreich aktualisiert");
}

async fn load_officer() {
	let reqwest_client = reqwest::Client::new();
	let (agents, maps, weapon_skins, buddies, sprays, player_cards, player_titles, content_tiers, queues, gamemodes, game_pods, seasons, flex, weapons, gear) = join!(
		fetch::<ValorantAPIAgents>(FetchOptions {
			url: String::from("https://valorant-api.com/v1/agents?isPlayableCharacter=true"),
			..FetchOptions::default()
		}),
		fetch::<ValorantAPIMaps>(FetchOptions {
			url: String::from("https://valorant-api.com/v1/maps"),
			..FetchOptions::default()
		}),
		fetch::<ValorantAPIWeaponSkins>(FetchOptions {
			url: String::from("https://valorant-api.com/v1/weapons/skins"),
			..FetchOptions::default()
		}),
		fetch::<ValorantAPIBuddys>(FetchOptions {
			url: String::from("https://valorant-api.com/v1/buddies"),
			..FetchOptions::default()
		}),
		fetch::<ValorantAPISprays>(FetchOptions {
			url: String::from("https://valorant-api.com/v1/sprays"),
			..FetchOptions::default()
		}),
		fetch::<ValorantAPIPlayerCards>(FetchOptions {
			url: String::from("https://valorant-api.com/v1/playercards"),
			..FetchOptions::default()
		}),
		fetch::<ValorantAPIPlayerTitles>(FetchOptions {
			url: String::from("https://valorant-api.com/v1/playertitles"),
			..FetchOptions::default()
		}),
		fetch::<ValorantAPIContentTiers>(FetchOptions {
			url: String::from("https://valorant-api.com/v1/contenttiers"),
			..FetchOptions::default()
		}),
		fetch::<ValorantAPIGamemodeQueues>(FetchOptions {
			url: String::from("https://valorant-api.com/v1/gamemodes/queues"),
			..FetchOptions::default()
		}),
		fetch::<ValorantAPIGamemodes>(FetchOptions {
			url: String::from("https://valorant-api.com/v1/gamemodes"),
			..FetchOptions::default()
		}),
		fetch::<ValorantAPIGamePodsData>(FetchOptions {
			url: String::from("https://cdn.henrikdev.xyz/valorant/v1/locres/en-us"),
			..FetchOptions::default()
		}),
		fetch::<ValorantAPISeasons>(FetchOptions {
			url: String::from("https://valorant-api.com/v1/seasons"),
			..FetchOptions::default()
		}),
		fetch::<ValorantAPIFlex>(FetchOptions {
			url: String::from("https://valorant-api.com/v1/flex"),
			..FetchOptions::default()
		}),
		get_weapons(&reqwest_client, None),
		get_gears(&reqwest_client, None)
	);

	match agents {
		Ok(v) => {
			OFFICER_API_AGENTS.store(Arc::new(v.data.data.clone()));
		}
		Err(e) => {
			eprintln!("[JOBS][OFFICER][AGENTS]: {:?}", e);
		}
	}
	match maps {
		Ok(v) => {
			OFFICER_API_MAPS.store(Arc::new(v.data.data.clone()));
			/*let mut maps = OFFICER_API_MAPS.write().await;
			 *maps = v.data.data;*/
		}
		Err(e) => {
			eprintln!("[JOBS][OFFICER][MAPS]: {:?}", e);
		}
	}
	match weapon_skins {
		Ok(v) => {
			OFFICER_API_SKINS.store(Arc::new(v.data.data.clone()));
		}
		Err(e) => {
			eprintln!("[JOBS][OFFICER][WEAPON_SKINS]: {:?}", e);
		}
	}
	match buddies {
		Ok(v) => {
			OFFICER_API_BUDDIES.store(Arc::new(v.data.data.clone()));
		}
		Err(e) => {
			eprintln!("[JOBS][OFFICER][BUDDIES]: {:?}", e);
		}
	}
	match sprays {
		Ok(v) => {
			OFFICER_API_SPRAYS.store(Arc::new(v.data.data.clone()));
		}
		Err(e) => {
			eprintln!("[JOBS][OFFICER][SPRAYS]: {:?}", e);
		}
	}
	match player_cards {
		Ok(v) => {
			OFFICER_API_PLAYER_CARDS.store(Arc::new(v.data.data.clone()));
		}
		Err(e) => {
			eprintln!("[JOBS][OFFICER][PLAYER_CARDS]: {:?}", e);
		}
	}
	match player_titles {
		Ok(v) => {
			OFFICER_API_PLAYER_TITLES.store(Arc::new(v.data.data.clone()));
		}
		Err(e) => {
			eprintln!("[JOBS][OFFICER][PLAYER_TITLES]: {:?}", e);
		}
	}
	match content_tiers {
		Ok(v) => {
			OFFICER_API_CONTENT_TIERS.store(Arc::new(v.data.data.clone()));
		}

		Err(e) => {
			eprintln!("[JOBS][OFFICER][CONTENT_TIERS]: {:?}", e);
		}
	}
	match gamemodes {
		Ok(v) => {
			OFFICER_API_CONTENT_GAMEMODES.store(Arc::new(v.data.data.clone()));
		}
		Err(e) => {
			eprintln!("[JOBS][OFFICER][GAMEMODES]: {:?}", e);
		}
	}
	match queues {
		Ok(v) => {
			OFFICER_API_CONTENT_GAMEMODES_QUEUES.store(Arc::new(v.data.data.clone()));
		}
		Err(e) => {
			eprintln!("[JOBS][OFFICER][GAME_MODES]: {:?}", e);
		}
	}
	match game_pods {
		Ok(v) => {
			OFFICER_API_GAME_PODS.store(Arc::new(v.data.UI_GamePodStrings.clone()));
		}
		Err(e) => {
			eprintln!("[JOBS][OFFICER][GAME_PODS]: {:?}", e);
		}
	}
	match weapons {
		Ok(v) => {
			OFFICER_API_WEAPONS.store(Arc::new(v.clone()));
		}
		Err(e) => {
			eprintln!("[JOBS][OFFICER][WEAPONS]: {:?}", e);
		}
	}
	match gear {
		Ok(v) => {
			OFFICER_API_GEARS.store(Arc::new(v.clone()));
		}
		Err(e) => {
			eprintln!("[JOBS][OFFICER][GEAR]: {:?}", e);
		}
	}
	match flex {
		Ok(v) => {
			OFFICER_API_FLEX.store(Arc::new(v.data.data.clone()));
		}
		Err(e) => {
			eprintln!("[JOBS][OFFICER][FLEX]: {:?}", e);
		}
	}
	match seasons {
		Ok(v) => {
			OFFICER_API_SEASONS.store(Arc::new(v.data.data.clone()));
			let mut short_ids: Vec<SeasonShortIdCombo> = Vec::new();

			// Filter for seasons (where parentUuid is null)
			let all_seasons = v.data.data
				.iter()
				.filter(|x| x.parentUuid.is_none())
				.collect::<Vec<_>>();

			// Track the last episode number to continue numbering for V seasons
			let mut last_episode_number = 9; // Assuming Episode 9 is the last numbered episode before V25
			// Ensure multiple containers of the same V season (e.g., V25-1, V25-2) share the same episode number
			let mut v_display_to_episode: HashMap<String, String> = HashMap::new();

			for season in all_seasons {
				// Determine the season ID based on the displayName
				let season_id = if season.displayName.starts_with("EPISODE") {
					// Extract the episode number for EPISODE seasons
					let eid = season.displayName.split(" ").last().unwrap().to_string();
					// Keep last_episode_number in sync in case EPISODE seasons appear before/after V seasons
					if let Ok(n) = eid.parse::<i32>() {
						if n > last_episode_number {
							last_episode_number = n;
						}
					}
					eid
				} else if season.displayName.starts_with("V") {
					// Assign a single episode number per V season label (e.g., both V25 containers -> same episode)
					let key = season.displayName.clone();
					if let Some(existing) = v_display_to_episode.get(&key) {
						existing.clone()
					} else {
						last_episode_number += 1;
						let assigned = last_episode_number.to_string();
						v_display_to_episode.insert(key, assigned.clone());
						assigned
					}
				} else {
					// Skip non-EPISODE and non-V seasons (e.g., Closed Beta)
					continue;
				};

				// Find all acts for this season (where parentUuid matches the season's UUID)
				let all_acts = v.data.data
					.iter()
					.filter(|x| {
						let parent = x.parentUuid.clone();
						if parent.is_none() {
							return false;
						}
						parent.unwrap() == season.uuid
					})
					.collect::<Vec<_>>();

				for act in all_acts {
					let act_id = act.displayName.split(" ").last().unwrap();
					let act_id = roman_to_number(act_id).unwrap_or_else(|| act_id.to_string());

					// Format the short_id in the episode format (eXaY)
					let short_id = format!("e{}a{}", season_id, act_id);

					short_ids.push(SeasonShortIdCombo {
						short_id,
						season: act.uuid.clone(),
					});
				}
			}

			let mut all_seasons = v.data.data.clone();
			all_seasons.sort_by(|a, b| b.endTime.cmp(&a.endTime));
			let c_season = all_seasons
				.iter()
				.find(|x| { x.startTime < DateTime::now() && x.endTime > DateTime::now() && x.type_.is_some() })
				.unwrap()
				.uuid.clone();

			VALORANT_REGULAR_CURRENT_SEASON.store(Arc::new(c_season));
			VALORANT_SEASON_SHORT_IDS.store(Arc::new(short_ids));
		}
		Err(e) => {
			eprintln!("[JOBS][OFFICER][GAME_PODS]: {:?}", e);
		}
	}
}

async fn sync_val_auth(client: &Client) {
	let db = get_db::<Auth>(&client, "auth", Some("API"))
		.find_one(doc! { "source": "val" }).await
		.unwrap()
		.unwrap();
	let access = db.access;
	let entitlement = db.entitlement;
	VALORANT_AUTH.store(
		Arc::new(
			HashMap::from([
				("authorization".to_string(), access),
				("entitlements".to_string(), entitlement),
			])
		)
	);
}

async fn sync_version(client: &Client) {
	let db = get_db::<Versions>(&client, "versions", Some("INGAME-API"))
		.find(doc! {}).await
		.unwrap();
	let versions: Vec<Versions> = db.try_collect().await.unwrap();
	VALORANT_VERSIONS.store(Arc::new(versions));
}

async fn update_premier_metadata_cache(client: &Client) {
	let season = get_db::<PremierSeasonDB>(&client, "premier_seasons", None)
		.find(doc! { "region": "eu" })
		.sort(doc! { "season.EndTime": -1 }).await;
	match season {
		Ok(v) => {
			let collected: Vec<PremierSeasonDB> = v.try_collect().await.unwrap_or_else(|e| {
				eprintln!("[JOBS][UPDATE_PREMIER_METADATA_CACHE_SEASON]: {:?}", e);
				vec![]
			});
			VALORANT_PREMIER_CURRENT_SEASON.store(Arc::new(collected.first().unwrap().season.ID.clone()));
		}
		Err(e) => {
			eprintln!("[JOBS][UPDATE_PREMIER_METADATA_CACHE_SEASON]: {:?}", e);
		}
	}
	let current_premier_season = get_c_season_premier().await;
	let conferences = get_db::<PremierConferencesDB>(&client, "premier_conferences", None).find(doc! { "season": current_premier_season.to_string() }).await;
	match conferences {
		Ok(v) => {
			let premier_conferences: Vec<PremierConferencesDB> = v.try_collect().await.unwrap();
			VALORANT_PREMIER_CONFERENCES.store(Arc::new(premier_conferences));
		}
		Err(e) => {
			eprintln!("[JOBS][UPDATE_PREMIER_METADATA_CACHE_CONFERENCES]: {:?}", e);
		}
	}
}

/*#[derive(Clone)]
struct AppState {
    sql: PgPool,
    pool: Pool,
    client: Client,
    hdd_client: HDDClient,
}*/
#[derive(Clone)]
struct AppState {
	sql: PgPool,
	redis: MultiplexedConnection,
	client: Client,
	hdd_client: HDDClient,
}

/*impl FromRef<AppState> for Pool {
    fn from_ref(state: &AppState) -> Self {
        state.pool.clone()
    }
}*/

impl FromRef<AppState> for MultiplexedConnection {
	fn from_ref(state: &AppState) -> Self {
		state.redis.clone()
	}
}

impl FromRef<AppState> for Client {
	fn from_ref(state: &AppState) -> Self {
		state.client.clone()
	}
}

impl FromRef<AppState> for HDDClient {
	fn from_ref(state: &AppState) -> Self {
		state.hdd_client.clone()
	}
}

impl FromRef<AppState> for PgPool {
	fn from_ref(state: &AppState) -> Self {
		state.sql.clone()
	}
}

fn set_rate_limit_headers(response: &mut Response, limit: i32, remaining: i32, reset: i32, cache: i32, bucket: &str, id: &str) {
	let headers = response.headers_mut();
	headers.insert("X-Version".parse::<HeaderName>().unwrap(), env::var("VERSION").unwrap().parse().unwrap());
	headers.insert("Access-Control-Allow-Origin".parse::<HeaderName>().unwrap(), "*".parse().unwrap());
	headers.insert("X-RateLimit-Limit".parse::<HeaderName>().unwrap(), limit.to_string().parse().unwrap());
	headers.insert("X-RateLimit-Remaining".parse::<HeaderName>().unwrap(), remaining.to_string().parse().unwrap());
	headers.insert("X-RateLimit-Reset".parse::<HeaderName>().unwrap(), reset.to_string().parse().unwrap());
	headers.insert("X-RateLimit-Bucket".parse::<HeaderName>().unwrap(), bucket.parse().unwrap());
	headers.insert("X-Request-ID".parse::<HeaderName>().unwrap(), id.parse().unwrap());
	if cache >= 0 {
		headers.insert("X-Cache-TTL".parse::<HeaderName>().unwrap(), cache.to_string().parse().unwrap());
		headers.insert("X-Cache-Status".parse::<HeaderName>().unwrap(), "HIT".parse().unwrap());
	} else {
		headers.insert("X-Cache-Status".parse::<HeaderName>().unwrap(), "NONE".parse().unwrap());
	}
}

async fn log_request_mongodb(client: &Client, instant: &Instant, status_code: i32, path: &str, bucket: &str, id: &str, token: &str) {
	let time = instant.elapsed().as_millis() as i32;
	let date = DateTime::now();

	let request_doc = doc! {
		"id": id,
		"date": date,
		"code": status_code,
		"req_ms": time,
		"path": path,
		"bucket": bucket,
		"token": token
	};

	let res = get_db::<Document>(client, "requests", Some("API")).insert_one(request_doc).await;

	if let Err(e) = res {
		eprintln!("Error logging request to MongoDB: {:?}", e);
	} else {
		if cfg!(debug_assertions) {
			println!("Logged request to MongoDB: {:?}", id);
		}
	}
}

async fn log_request_sql(pool: &sqlx::pool::Pool<Postgres>, instant: &Instant, status_code: i32, path: &str, bucket: &str, id: &str, token: &str) {
	let time = instant.elapsed().as_millis() as i16;
	let date = Utc::now();

	let res = sqlx
		::query("INSERT INTO public.requests (id, date, code, req_ms, path, bucket, token) VALUES ($1::uuid, $2, $3, $4, $5, $6::uuid, $7)")
		.bind(id.to_string())
		.bind(date)
		.bind(status_code)
		.bind(time)
		.bind(path)
		.bind(bucket.to_string())
		.bind(token)
		.execute(pool).await;
	if let Err(e) = res {
		eprintln!("Error logging request: {:?}", e);
	} else {
		if cfg!(debug_assertions) {
			println!("Logged request: {:?}", id);
		}
	}
}

async fn rate_limiting(Query(querystring): Query<HashMap<String, String>>, State(state): State<Arc<AppState>>, mut req: Request, next: Next) -> Response {
	let instant = Instant::now();
	let req_headers = req.headers();
	let token = req_headers.get("authorization");
	let api_key = if let Some(token) = token {
		Some(token.to_str().unwrap().to_string())
	} else if querystring.get("api_key").is_some() {
		Some(querystring.get("api_key").unwrap().to_string())
	} else {
		None
	};
	let path = req.uri().path();

	let api_key = match api_key {
		Some(key) => key,
		None => {
			if path.contains("openapi.json") || path.contains("docs") {
				return next.run(req).await;
			}
			return error_handler(vec![ErrorCodes::Base401]);
		}
	};

	let mut conn = state.redis.clone();
	//let redis_pool = state.pool.clone();
	let client = state.client.clone();
	let sql = state.sql.clone();

	// Ensure Redis connection is dropped after use
	/*let mut conn = match redis.get().await {
        Ok(conn) => conn,
        Err(_) => return error_handler(vec![ErrorCodes::Base401]),
    };*/

	let redis_key = format!("api;key;{}", api_key);
	let redis_result: RedisResult<HashMap<String, String>> = conn.hgetall(&redis_key).await;
	let (mut limit, mut used, mut bucket, path_and_query) = (0, 0, "".to_string(), req.uri().path_and_query().cloned().unwrap().to_string());
	if let Ok(redis_limit) = redis_result {
		let req_id = Uuid::new_v4().to_string();
		if !redis_limit.is_empty() {
			let max_limit = redis_limit.get("limit").unwrap_or(&(-1).to_string()).parse::<i32>().ok();
			let used_ = redis_limit.get("used").unwrap_or(&(-1).to_string()).parse::<i32>().ok();
			let bucket_ = redis_limit.get("bucket");
			if max_limit.is_none() || used_.is_none() || bucket_.is_none() {
				return error_handler(vec![ErrorCodes::Base401]);
			}
			let max_limit = max_limit.unwrap();
			let used_ = used_.unwrap();
			if used_ >= max_limit {
				let ttl = conn.ttl(&redis_key).await.unwrap_or(0);
				if ttl < 0 {
					let _: () = conn.del(&redis_key).await.unwrap();
				}
				let mut response = error_handler(vec![ErrorCodes::Base429]);
				let bucket_ = bucket_.unwrap();
				set_rate_limit_headers(&mut response, max_limit, 0, ttl, -1, bucket_, &req_id);
				log_request_mongodb(&client, &instant, response.status().as_u16() as i32, &path_and_query, &bucket_, &req_id, &api_key).await;
				return response;
			}
			used = used_;
			limit = max_limit;
			bucket = bucket_.unwrap().clone();
		} else {
			let api_key_db = get_db::<APITokens>(&client, "tokens", Some("API"))
				.find_one(doc! { "token": api_key.clone() }).await
				.unwrap();
			if api_key_db.is_none() {
				return error_handler(vec![ErrorCodes::Base401]);
			}
			let api_key_db = api_key_db.unwrap();
			let bucket_ = Uuid::new_v4().to_string();
			let _: () = conn
				.hset_multiple(
					&redis_key,
					&[
						("limit", api_key_db.limit.to_string()),
						("used", (0).to_string()),
						("bucket", bucket_.clone()),
					]
				).await
				.unwrap_or(());
			let _: () = conn.expire(&redis_key, 60).await.unwrap_or(());
			limit = api_key_db.limit as i32;
			bucket = bucket_.to_string();
		}

		let custom_rl = Arc::new(RateLimiting {
			redis_cache_ttl: AtomicIsize::new(-1),
			background_requests: AtomicUsize::new(1),
		});

		req.extensions_mut().insert(custom_rl.clone());

		let mut response = next.run(req).await;
		let atomic_use = custom_rl.background_requests.load(Ordering::SeqCst);
		let atomic_cache = custom_rl.redis_cache_ttl.load(Ordering::SeqCst);

		let recheck: RedisResult<HashMap<String, String>> = conn.hgetall(&redis_key).await;
		if let Ok(recheck) = recheck {
			if !recheck.is_empty() {
				let _: () = conn.hincr(&redis_key, "used", atomic_use as i32).await.unwrap_or(());
				let ttl = conn.ttl(&redis_key).await.unwrap_or(0);
				if ttl < 0 {
					let _: () = conn.del(&redis_key).await.unwrap();
				}
				set_rate_limit_headers(&mut response, limit, limit - used - (atomic_use as i32), ttl, atomic_cache as i32, &bucket, &req_id);
				log_request_mongodb(&client, &instant, response.status().as_u16() as i32, &path_and_query, &bucket, &req_id, &api_key).await;
			} else {
				let api_key_db = get_db::<APITokens>(&client, "tokens", Some("API"))
					.find_one(doc! { "token": api_key.clone() }).await
					.unwrap();
				let api_key_db = api_key_db.unwrap();
				let bucket_ = Uuid::new_v4().to_string();
				let _: () = conn
					.hset_multiple(
						&redis_key,
						&[
							("limit", api_key_db.limit.to_string()),
							("used", (0).to_string()),
							("bucket", bucket_.clone()),
						]
					).await
					.unwrap_or(());
				let _: () = conn.expire(&redis_key, 60).await.unwrap_or(());
				set_rate_limit_headers(&mut response, limit, limit - used - (atomic_use as i32), 60, atomic_cache as i32, &bucket, &req_id);
				log_request_mongodb(&client, &instant, response.status().as_u16() as i32, &path_and_query, &bucket_, &req_id, &api_key).await;
			}
		} else {
			eprintln!("Redis Error: {:?}", recheck.err());
		}
		response
	} else {
		eprintln!("Redis Error: {:?}", redis_result.err());
		let mut r = next.run(req).await;
		//CORS headers
		let mut headers = r.headers_mut();
		headers.insert("Access-Control-Allow-Origin", "*".parse().unwrap());
		r
	}
}

#[tokio::main]
async fn main() {
	dotenv().ok();
	if cfg!(debug_assertions) {
		env_logger::init_from_env(env_logger::Env::new().default_filter_or("info"));
		unsafe {
			env::set_var("RUST_BACKTRACE", "full");
		}
	} else {
		env_logger::init_from_env(env_logger::Env::new().default_filter_or("info"));
	}

	init_http_clients();

	println!("[MONGO_DB] Init MongoDB");
	let client_options = ClientOptions::parse(env::var("MONGOURI").unwrap().as_str()).await.expect("failed to parse options");
	let client = Client::with_options(client_options).expect("failed to connect");
	println!("[MONGO_DB] Connected");

	println!("[MONGO_DB_HDD] Init MongoDB");
	let client_options_hdd = ClientOptions::parse(env::var("MONGOURI_HDD").unwrap().as_str()).await.expect("failed to parse options");
	let client_hdd = Client::with_options(client_options_hdd).expect("failed to connect");
	println!("[MONGO_DB_HDD] Connected");

	println!("[REDIS] Init Redis");
	let redis_client = redis::Client::open(env::var("REDIS").unwrap()).unwrap();
	let redis_con = redis_client.get_multiplexed_tokio_connection().await.unwrap();
	println!("[REDIS] Connected");

	println!("[SQL] Init SQL");
	let sql = PgPoolOptions::new().max_connections(5).connect(env::var("POSTGRES").unwrap().as_str()).await.expect("Failed to connect to SQL");
	println!("[SQL] Connected");

	println!("[JOBS] Starting JOBS");
	{
		let file = fs::read_to_string("./files/valorant/premier/TeamIconColorsData.json");
		if file.is_err() {
			eprintln!("[VALORANT_PREMIER_COLORS]: {:?}", file.err());
		} else {
			let file = file.unwrap();
			let parse: Vec<FModelPremierColors> = serde_json::from_str(&file).unwrap();
			let colors_: Vec<FModelPremierColorsRow> = parse.first().unwrap().Rows.values().cloned().collect();
			VALORANT_PREMIER_COLORS.store(Arc::new(colors_));
		}
	}
	sync_val_auth(&client).await;
	update_premier_metadata_cache(&client).await;
	load_officer().await;
	sync_version(&client).await;
	update_b2().await;
	//test
	//update_premier_metadata_database_job(client.clone()).await;
	tokio::spawn(officer_api_job());
	tokio::spawn(website_job(client.clone()));
	tokio::spawn(sync_val_auth_job(client.clone()));
	tokio::spawn(sync_val_versions_job(client.clone()));
	//tokio::spawn(proxy_update_job(env::var("WEBSHARE").unwrap()));
	tokio::spawn(update_premier_teams_database_job(client.clone()));
	tokio::spawn(update_premier_metadata_database_job(client.clone()));
	tokio::spawn(update_player_leaderboard_job(client.clone()));
	tokio::spawn(update_b2_job());
	//tokio::spawn(mongo_to_s3_migration_wrapper(client.clone(), client_hdd.clone()));

	println!("[SERVER] Starting Server");

	let state = Arc::new(AppState {
		sql,
		redis: redis_con,
		client: client.clone(),
		hdd_client: HDDClient(client_hdd.clone()),
	});

	let app = Router::new()
		.merge(SwaggerUi::new("/docs").url("/openapi.json", openapi::ApiDoc::openapi()))
		.route("/admin/v1/jobs/{type}", get(routes::admin::jobs::jobs))
		.route("/assist/v1/names", post(routes::assist::names::assist_names))
		.route("/proxy/v1/mc/valo_dach/whitelist/list", get(routes::proxy::mc::valo_dach_whitelist_get))
		.route("/proxy/v1/mc/valo_dach/whitelist/{name}", post(routes::proxy::mc::valo_dach_whitelist_add))
		.route("/proxy/v1/mc/valo_dach/whitelist/{name}", delete(routes::proxy::mc::valo_dach_whitelist_remove))
		.route("/valorant/v1/account/{name}/{tag}", get(routes::valorant::account::get_account_v1))
		.route("/valorant/v2/account/{name}/{tag}", get(routes::valorant::account::get_account_v2))
		.route("/valorant/v1/by-puuid/account/{puuid}", get(routes::valorant::account::get_account_by_id_v1))
		.route("/valorant/v2/by-puuid/account/{puuid}", get(routes::valorant::account::get_account_by_id_v2))
		.route("/valorant/v1/by-puuid/mmr/{affinity}/{puuid}", get(routes::valorant::mmr::get_mmr_v1_by_id))
		.route("/valorant/v2/by-puuid/mmr/{affinity}/{puuid}", get(routes::valorant::mmr::get_mmr_v2_by_id))
		.route("/valorant/v3/by-puuid/mmr/{affinity}/{platform}/{puuid}", get(routes::valorant::mmr::get_mmr_v3_by_id))
		.route("/valorant/v1/by-puuid/mmr-history/{affinity}/{puuid}", get(routes::valorant::mmr_history::get_mmr_history_by_id))
		.route("/valorant/v2/by-puuid/mmr-history/{affinity}/{platform}/{puuid}", get(routes::valorant::mmr_history::get_mmr_history_v2_by_id))
		.route("/valorant/v3/by-puuid/matches/{affinity}/{puuid}", get(routes::valorant::matches::get_matches_v3_by_id))
		.route("/valorant/v4/by-puuid/matches/{affinity}/{platform}/{puuid}", get(routes::valorant::matches::get_matches_v4_by_id))
		.route("/valorant/v1/by-puuid/lifetime/matches/{affinity}/{puuid}", get(routes::valorant::stored_endpoints::stored_matches_by_id))
		.route("/valorant/v1/by-puuid/lifetime/mmr-history/{affinity}/{puuid}", get(routes::valorant::stored_endpoints::stored_mmr_history_by_id))
		.route("/valorant/v1/by-puuid/stored-matches/{affinity}/{puuid}", get(routes::valorant::stored_endpoints::stored_matches_by_id))
		.route("/valorant/v1/by-puuid/stored-mmr-history/{affinity}/{puuid}", get(routes::valorant::stored_endpoints::stored_mmr_history_by_id))
		.route("/valorant/v2/by-puuid/stored-mmr-history/{affinity}/{platform}/{puuid}", get(routes::valorant::stored_endpoints::stored_mmr_history_v2_by_id))
		.route("/valorant/v1/crosshair/generate", get(routes::valorant::crosshair::crosshair))
		.route("/valorant/v1/content", get(routes::valorant::content::get_content_v1))
		.route("/valorant/v1/esports/schedule", get(routes::valorant::esports::esports_schedules_v1))
		.route("/valorant/v1/leaderboard/{affinity}", get(routes::valorant::leaderboard::leaderboard_v1))
		.route("/valorant/v2/leaderboard/{affinity}", get(routes::valorant::leaderboard::leaderboard_v2))
		.route("/valorant/v3/leaderboard/{affinity}/{platform}", get(routes::valorant::leaderboard::leaderboard_v3))
		.route("/valorant/v1/lifetime/matches/{affinity}/{name}/{tag}", get(routes::valorant::stored_endpoints::stored_matches))
		.route("/valorant/v1/lifetime/mmr-history/{affinity}/{name}/{tag}", get(routes::valorant::stored_endpoints::stored_mmr_history))
		.route("/valorant/v2/match/{match_id}", get(routes::valorant::match_details::match_v2))
		.route("/valorant/v4/match/{affinity}/{match_id}", get(routes::valorant::match_details::match_v4))
		.route("/valorant/v3/matches/{affinity}/{name}/{tag}", get(routes::valorant::matches::get_matches_v3_by_name))
		.route("/valorant/v4/matches/{affinity}/{platform}/{name}/{tag}", get(routes::valorant::matches::get_matches_v4_by_name))
		.route("/valorant/v1/mmr/{affinity}/{name}/{tag}", get(routes::valorant::mmr::get_mmr_v1_by_name))
		.route("/valorant/v2/mmr/{affinity}/{name}/{tag}", get(routes::valorant::mmr::get_mmr_v2_by_name))
		.route("/valorant/v3/mmr/{affinity}/{platform}/{name}/{tag}", get(routes::valorant::mmr::get_mmr_v3_by_name))
		.route("/valorant/v1/mmr-history/{affinity}/{name}/{tag}", get(routes::valorant::mmr_history::get_mmr_history_by_name))
		.route("/valorant/v2/mmr-history/{affinity}/{platform}/{name}/{tag}", get(routes::valorant::mmr_history::get_mmr_history_v2_by_name))
		.route("/valorant/v1/premier/leaderboard/{affinity}", get(routes::valorant::premier::premier_leaderboard))
		.route("/valorant/v1/premier/leaderboard/{affinity}/{conference}", get(routes::valorant::premier::premier_leaderboard))
		.route("/valorant/v1/premier/leaderboard/{affinity}/{conference}/{division}", get(routes::valorant::premier::premier_leaderboard))
		.route("/valorant/v1/premier/seasons/{affinity}", get(routes::valorant::premier::premier_seasons))
		.route("/valorant/v1/premier/conferences", get(routes::valorant::premier::premier_conferences))
		.route("/valorant/v1/premier/search", get(routes::valorant::premier::premier_search))
		.route("/valorant/v1/premier/{id}", get(routes::valorant::premier::premier_by_id))
		.route("/valorant/v1/premier/{id}/history", get(routes::valorant::premier::premier_by_id_history))
		.route("/valorant/v1/premier/{name}/{tag}", get(routes::valorant::premier::premier_by_name))
		.route("/valorant/v1/premier/{name}/{tag}/history", get(routes::valorant::premier::premier_by_name_history))
		.route("/valorant/v1/queue-status/{affinity}", get(routes::valorant::queue_status::QueueStatus))
		.route("/valorant/v1/raw", post(routes::valorant::raw::Raw))
		.route("/valorant/v1/status/{affinity}", get(routes::valorant::status::Status))
		.route("/valorant/{version}/store-featured", get(routes::valorant::store_featured::StoreFeatured))
		.route("/valorant/{version}/store-offers", get(routes::valorant::store_offers::StoreOffers))
		.route("/valorant/v1/stored-matches/{affinity}/{name}/{tag}", get(routes::valorant::stored_endpoints::stored_matches))
		.route("/valorant/v1/stored-mmr-history/{affinity}/{name}/{tag}", get(routes::valorant::stored_endpoints::stored_mmr_history))
		.route("/valorant/v2/stored-mmr-history/{affinity}/{platform}/{name}/{tag}", get(routes::valorant::stored_endpoints::stored_mmr_history_v2))
		.route("/valorant/v1/version/{affinity}", get(routes::valorant::version::Version))
		.route("/valorant/v1/website/{country_code}", get(routes::valorant::website::Website))
		.route("/valorantlabs/v1/matches/{affinity}/{puuid}", get(routes::valorantlabs::matches::get_matches))
		.with_state(state.clone())
		.fallback(default)
		.layer(from_fn_with_state(state, rate_limiting));

	let listener = tokio::net::TcpListener::bind(format!("127.0.0.1:{}", env::var("PORT").unwrap().parse::<u16>().unwrap())).await.unwrap();
	axum::serve(listener, app).await.unwrap();
}
